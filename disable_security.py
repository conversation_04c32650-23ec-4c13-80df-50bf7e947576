#!/usr/bin/env python3
"""
Android ROM Security Disabler
يقوم بإزالة الحماية من ROM الأندرويد
"""

import os
import shutil
import subprocess
import sys

def run_command(cmd):
    """تشغيل أمر في النظام"""
    print(f"تشغيل: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"خطأ: {result.stderr}")
        return False
    print(f"نجح: {result.stdout}")
    return True

def disable_vbmeta():
    """تعطيل ملفات vbmeta للحماية"""
    print("=== تعطيل ملفات vbmeta ===")
    
    # إنشاء ملفات vbmeta فارغة
    vbmeta_files = ['vbmeta.img', 'vbmeta_system.img', 'vbmeta_vendor.img']
    
    for vbmeta_file in vbmeta_files:
        if os.path.exists(vbmeta_file):
            # نسخ احتياطي
            shutil.copy(vbmeta_file, f"{vbmeta_file}.backup")
            print(f"تم إنشاء نسخة احتياطية: {vbmeta_file}.backup")
            
            # إنشاء ملف فارغ
            with open(vbmeta_file, 'wb') as f:
                f.write(b'\x00' * 65536)  # 64KB من الأصفار
            print(f"تم تعطيل: {vbmeta_file}")

def modify_boot_img():
    """تعديل boot.img لإزالة القيود"""
    print("=== تعديل boot.img ===")
    
    if not os.path.exists('boot.img'):
        print("ملف boot.img غير موجود")
        return False
    
    # نسخ احتياطي
    shutil.copy('boot.img', 'boot.img.backup')
    print("تم إنشاء نسخة احتياطية: boot.img.backup")
    
    # استخراج boot.img (يحتاج أدوات إضافية)
    print("ملاحظة: تعديل boot.img يحتاج أدوات متخصصة")
    return True

def create_magisk_patched():
    """إنشاء ملف boot مُعدل بـ Magisk"""
    print("=== إعداد Magisk ===")
    print("لتثبيت Magisk، ستحتاج إلى:")
    print("1. تطبيق Magisk Manager")
    print("2. تصحيح boot.img من خلال التطبيق")
    print("3. استبدال boot.img بالملف المُصحح")

def disable_dm_verity():
    """تعطيل dm-verity"""
    print("=== تعطيل dm-verity ===")
    
    # تعديل fstab لتعطيل dm-verity
    print("تعطيل dm-verity في النظام...")
    
    # هذا يتطلب تعديل ملفات النظام
    print("ملاحظة: تعطيل dm-verity يتطلب root access")

def create_flash_script():
    """إنشاء سكريبت للفلاش"""
    flash_script = """@echo off
echo ===== فلاش ROM معدل =====
echo تأكد من أن الجهاز في وضع fastboot

echo فلاش vbmeta معطل...
fastboot flash vbmeta vbmeta_disabled.img
fastboot flash vbmeta_system vbmeta_system_disabled.img
fastboot flash vbmeta_vendor vbmeta_vendor_disabled.img

echo فلاش boot.img...
fastboot flash boot boot.img

echo فلاش recovery.img...
fastboot flash recovery recovery.img

echo فلاش super.img...
fastboot flash super super.img

echo إعادة تشغيل...
fastboot reboot

echo تم الانتهاء!
pause
"""
    
    with open('flash_modified_rom.bat', 'w', encoding='utf-8') as f:
        f.write(flash_script)
    print("تم إنشاء سكريبت الفلاش: flash_modified_rom.bat")

def main():
    """الدالة الرئيسية"""
    print("=== أداة إزالة الحماية من ROM الأندرويد ===")
    print("تحذير: هذا قد يؤدي إلى تلف الجهاز!")
    
    # التحقق من وجود الملفات
    required_files = ['boot.img', 'recovery.img', 'super.img', 'vbmeta.img']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"ملفات مفقودة: {missing_files}")
        return False
    
    # تعطيل الحماية
    disable_vbmeta()
    modify_boot_img()
    create_magisk_patched()
    disable_dm_verity()
    create_flash_script()
    
    print("\n=== تم الانتهاء ===")
    print("الملفات المُعدلة جاهزة للفلاش")
    print("استخدم flash_modified_rom.bat للفلاش")
    
    return True

if __name__ == "__main__":
    main()
