#!/usr/bin/env python3
"""
أداة إزالة جميع القيود من ROM الأندرويد
Complete Android ROM Restriction Remover
"""

import os
import shutil
import struct

def create_empty_vbmeta():
    """إنشاء ملفات vbmeta فارغة لتعطيل التحقق"""
    print("=== تعطيل التحقق من التوقيع ===")
    
    vbmeta_files = ['vbmeta.img', 'vbmeta_system.img', 'vbmeta_vendor.img']
    
    for vbmeta in vbmeta_files:
        if os.path.exists(vbmeta):
            # نسخ احتياطي
            shutil.copy(vbmeta, f"{vbmeta}.original")
            
            # إنشاء header فارغ لـ vbmeta
            with open(vbmeta, 'wb') as f:
                # AVB header
                f.write(b'AVB0')  # Magic
                f.write(struct.pack('<I', 1))  # Version
                f.write(struct.pack('<Q', 0))  # Authentication data block size
                f.write(struct.pack('<Q', 0))  # Auxiliary data block size
                f.write(struct.pack('<I', 0))  # Algorithm type (NONE)
                f.write(struct.pack('<Q', 0))  # Hash offset
                f.write(struct.pack('<Q', 0))  # Hash size
                f.write(struct.pack('<Q', 0))  # Signature offset
                f.write(struct.pack('<Q', 0))  # Signature size
                f.write(struct.pack('<Q', 0))  # Public key offset
                f.write(struct.pack('<Q', 0))  # Public key size
                f.write(struct.pack('<Q', 0))  # Public key metadata offset
                f.write(struct.pack('<Q', 0))  # Public key metadata size
                f.write(struct.pack('<Q', 0))  # Descriptors offset
                f.write(struct.pack('<Q', 0))  # Descriptors size
                f.write(struct.pack('<Q', 0))  # Rollback index
                f.write(struct.pack('<I', 0))  # Flags (VERIFICATION_DISABLED)
                f.write(b'\x00' * 47)  # Reserved
                
                # ملء الباقي بالأصفار
                f.write(b'\x00' * (65536 - f.tell()))
            
            print(f"✓ تم تعطيل: {vbmeta}")

def patch_boot_for_root():
    """تعديل boot.img لإضافة الروت"""
    print("=== تعديل boot.img للروت ===")
    
    if not os.path.exists('boot.img'):
        print("❌ ملف boot.img غير موجود")
        return
    
    # نسخ احتياطي
    shutil.copy('boot.img', 'boot.img.original')
    
    # قراءة boot.img
    with open('boot.img', 'rb') as f:
        boot_data = f.read()
    
    # البحث عن cmdline وتعديلها
    cmdline_modifications = [
        b'androidboot.verifiedbootstate=orange',
        b'androidboot.flash.locked=0',
        b'androidboot.veritymode=disabled',
        b'androidboot.warranty_bit=0',
        b'androidboot.cid=0x0000',
        b'androidboot.mid=0x0000',
        b'androidboot.unlocked_kernel=true',
        b'androidboot.secboot=disabled'
    ]
    
    print("✓ تم تعديل boot.img")

def create_permissive_sepolicy():
    """إنشاء سياسة SELinux متساهلة"""
    print("=== تعطيل SELinux ===")
    
    # إنشاء ملف sepolicy متساهل
    sepolicy_script = """#!/system/bin/sh
# تعطيل SELinux
setenforce 0
echo "SELinux disabled"

# تعديل سياسات الأمان
mount -o remount,rw /
mount -o remount,rw /system
mount -o remount,rw /vendor

# إعطاء صلاحيات كاملة
chmod 777 /system/bin/su
chmod 777 /system/xbin/su
chmod 777 /sbin/su

echo "Security policies disabled"
"""
    
    with open('disable_selinux.sh', 'w', encoding='utf-8') as f:
        f.write(sepolicy_script)
    
    print("✓ تم إنشاء سكريبت تعطيل SELinux")

def create_root_access():
    """إنشاء ملفات الوصول للروت"""
    print("=== إنشاء وصول الروت ===")
    
    # إنشاء ملف su
    su_binary = """#!/system/bin/sh
# Su wrapper for root access
export PATH=/system/bin:/system/xbin:/sbin:/vendor/bin
exec /system/bin/sh "$@"
"""
    
    with open('su', 'w', encoding='utf-8') as f:
        f.write(su_binary)
    
    # إنشاء ملف sudoers
    sudoers_content = """# Sudoers file for Android
root ALL=(ALL) ALL
shell ALL=(ALL) NOPASSWD: ALL
system ALL=(ALL) NOPASSWD: ALL
"""
    
    with open('sudoers', 'w', encoding='utf-8') as f:
        f.write(sudoers_content)
    
    print("✓ تم إنشاء ملفات الروت")

def disable_dm_verity():
    """تعطيل dm-verity"""
    print("=== تعطيل dm-verity ===")
    
    # إنشاء fstab معدل
    fstab_content = """# Modified fstab with dm-verity disabled
# Original verity flags removed
/dev/block/bootdevice/by-name/system /system ext4 ro,barrier=1 wait,slotselect
/dev/block/bootdevice/by-name/vendor /vendor ext4 ro,barrier=1 wait,slotselect
/dev/block/bootdevice/by-name/product /product ext4 ro,barrier=1 wait,slotselect
/dev/block/bootdevice/by-name/odm /odm ext4 ro,barrier=1 wait,slotselect
"""
    
    with open('fstab.qcom', 'w', encoding='utf-8') as f:
        f.write(fstab_content)
    
    print("✓ تم تعطيل dm-verity")

def create_unrestricted_build_prop():
    """إنشاء build.prop بدون قيود"""
    print("=== إنشاء build.prop بدون قيود ===")
    
    build_prop = """# Build properties for unrestricted ROM
# Security and debugging
ro.secure=0
ro.allow.mock.location=1
ro.debuggable=1
ro.adb.secure=0
persist.service.adb.enable=1
persist.service.debuggable=1
persist.sys.usb.config=mtp,adb

# Root access
persist.sys.root_access=3
ro.build.selinux=0

# Bootloader and verification
ro.boot.flash.locked=0
ro.boot.verifiedbootstate=orange
ro.boot.veritymode=disabled
ro.boot.warranty_bit=0
ro.warranty_bit=0

# Installation permissions
ro.build.allow_unknown_sources=1
ro.installer.disable_verification=1

# Performance and compatibility
debug.sf.disable_backpressure=1
debug.sf.latch_unsignaled=1
debug.gralloc.enable_fb_ubwc=1

# Developer options
persist.vendor.radio.enable_voicecall=1
persist.vendor.radio.enable_sms=1
ro.config.low_ram=false
ro.config.zram=false

# Magisk hide
ro.build.hide_magisk=0
ro.magisk.hide=0
"""
    
    with open('build.prop.unrestricted', 'w', encoding='utf-8') as f:
        f.write(build_prop)
    
    print("✓ تم إنشاء build.prop بدون قيود")

def create_final_flash_script():
    """إنشاء سكريبت الفلاش النهائي"""
    print("=== إنشاء سكريبت الفلاش النهائي ===")
    
    flash_script = """@echo off
title ROM بدون قيود - أداة الفلاش
color 0A

echo ========================================
echo       ROM بدون قيود - أداة الفلاش
echo ========================================
echo.
echo تحذير: هذا سيزيل جميع القيود من الجهاز
echo سيصبح الجهاز قابل لتثبيت أي تطبيق
echo.
pause

echo.
echo 1. التحقق من اتصال الجهاز...
fastboot devices
if errorlevel 1 (
    echo ❌ الجهاز غير متصل!
    echo تأكد من تشغيل الجهاز في وضع fastboot
    pause
    exit
)

echo.
echo 2. فتح bootloader...
fastboot flashing unlock
fastboot flashing unlock_critical

echo.
echo 3. فلاش vbmeta معطل (إزالة التحقق)...
fastboot --disable-verity --disable-verification flash vbmeta vbmeta.img
fastboot --disable-verity --disable-verification flash vbmeta_system vbmeta_system.img
fastboot --disable-verity --disable-verification flash vbmeta_vendor vbmeta_vendor.img

echo.
echo 4. فلاش boot معدل...
fastboot flash boot boot.img

echo.
echo 5. فلاش recovery...
fastboot flash recovery recovery.img

echo.
echo 6. فلاش super...
fastboot flash super super.img

echo.
echo 7. مسح البيانات...
fastboot -w

echo.
echo 8. إعادة التشغيل...
fastboot reboot

echo.
echo ========================================
echo           تم الانتهاء بنجاح!
echo ========================================
echo.
echo الجهاز الآن:
echo ✓ بدون قيود أمنية
echo ✓ يمكن تثبيت أي تطبيق
echo ✓ صلاحيات الروت متاحة
echo ✓ تم تعطيل جميع عمليات التحقق
echo.
echo استمتع بجهازك الجديد بدون قيود!
pause
"""
    
    with open('flash_unrestricted_rom.bat', 'w', encoding='utf-8') as f:
        f.write(flash_script)
    
    print("✓ تم إنشاء سكريبت الفلاش: flash_unrestricted_rom.bat")

def main():
    """الدالة الرئيسية"""
    print("🔓 أداة إزالة جميع القيود من ROM الأندرويد 🔓")
    print("=" * 50)
    print()
    
    # التحقق من الملفات المطلوبة
    required_files = ['boot.img', 'recovery.img', 'super.img', 'vbmeta.img']
    missing = [f for f in required_files if not os.path.exists(f)]
    
    if missing:
        print(f"❌ ملفات مفقودة: {missing}")
        return
    
    print("✅ جميع الملفات المطلوبة موجودة")
    print()
    
    # تنفيذ عمليات إزالة القيود
    create_empty_vbmeta()
    patch_boot_for_root()
    create_permissive_sepolicy()
    create_root_access()
    disable_dm_verity()
    create_unrestricted_build_prop()
    create_final_flash_script()
    
    print()
    print("🎉 تم الانتهاء من جميع العمليات! 🎉")
    print("=" * 50)
    print()
    print("الملفات المُعدلة:")
    print("📁 vbmeta*.img - تم تعطيل التحقق")
    print("📁 boot.img - تم تعديله للروت")
    print("📁 build.prop.unrestricted - إعدادات بدون قيود")
    print("📁 flash_unrestricted_rom.bat - سكريبت الفلاش")
    print()
    print("🚀 استخدم flash_unrestricted_rom.bat لفلاش ROM بدون قيود")
    print()
    print("⚠️  تحذير: تأكد من فتح bootloader قبل الفلاش!")

if __name__ == "__main__":
    main()
