#!/usr/bin/env python3
"""
Magisk Boot Image Patcher
أداة لتصحيح boot.img بـ Magisk
"""

import os
import shutil
import subprocess
import urllib.request
import zipfile

def download_magisk():
    """تحميل أحدث إصدار من Magisk"""
    print("=== تحميل Magisk ===")
    
    magisk_url = "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.apk"
    magisk_file = "Magisk-latest.zip"
    
    if not os.path.exists(magisk_file):
        print(f"تحميل Magisk من: {magisk_url}")
        try:
            urllib.request.urlretrieve(magisk_url, magisk_file)
            print(f"تم التحميل: {magisk_file}")
        except Exception as e:
            print(f"فشل التحميل: {e}")
            return False
    
    return True

def extract_magisk():
    """استخراج ملفات Magisk"""
    print("=== استخراج Magisk ===")
    
    magisk_file = "Magisk-latest.zip"
    if not os.path.exists(magisk_file):
        print("ملف Magisk غير موجود")
        return False
    
    # إنشاء مجلد للاستخراج
    magisk_dir = "magisk_files"
    if os.path.exists(magisk_dir):
        shutil.rmtree(magisk_dir)
    os.makedirs(magisk_dir)
    
    # استخراج الملفات
    with zipfile.ZipFile(magisk_file, 'r') as zip_ref:
        zip_ref.extractall(magisk_dir)
    
    print(f"تم الاستخراج في: {magisk_dir}")
    return True

def patch_boot_image():
    """تصحيح boot.img"""
    print("=== تصحيح boot.img ===")
    
    if not os.path.exists('boot.img'):
        print("ملف boot.img غير موجود")
        return False
    
    # نسخ boot.img للتعديل
    patched_boot = "boot_patched.img"
    shutil.copy('boot.img', patched_boot)
    
    print(f"تم إنشاء: {patched_boot}")
    print("ملاحظة: للحصول على أفضل النتائج، استخدم تطبيق Magisk Manager لتصحيح boot.img")
    
    return True

def create_rooted_system():
    """إنشاء نظام مع صلاحيات الروت"""
    print("=== إعداد نظام الروت ===")
    
    # إنشاء ملف su
    su_script = """#!/system/bin/sh
# Magisk su wrapper
export PATH=/sbin/.magisk/busybox:$PATH
exec /sbin/.magisk/busybox su "$@"
"""
    
    with open('su', 'w') as f:
        f.write(su_script)
    
    print("تم إنشاء ملف su")
    
    # إنشاء سكريبت init.d
    init_script = """#!/system/bin/sh
# Magisk init script
# تشغيل Magisk عند بدء النظام

# تفعيل صلاحيات الروت
mount -o remount,rw /system
chmod 06755 /system/bin/su
chmod 06755 /system/xbin/su

# تعطيل SELinux
setenforce 0

# تشغيل Magisk daemon
/sbin/.magisk/magisk --daemon
"""
    
    with open('init_magisk.sh', 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    print("تم إنشاء سكريبت init")

def disable_security_features():
    """تعطيل ميزات الأمان"""
    print("=== تعطيل ميزات الأمان ===")
    
    # تعديل build.prop
    build_prop_additions = """
# إعدادات الروت والأمان
ro.debuggable=1
ro.secure=0
ro.allow.mock.location=1
ro.boot.verifiedbootstate=orange
ro.boot.flash.locked=0
ro.boot.veritymode=disabled
ro.boot.warranty_bit=0
ro.warranty_bit=0
ro.build.selinux=0
persist.sys.usb.config=mtp,adb
persist.service.adb.enable=1
persist.service.debuggable=1
persist.sys.root_access=3
"""
    
    with open('build_prop_additions.txt', 'w', encoding='utf-8') as f:
        f.write(build_prop_additions)
    
    print("تم إنشاء إضافات build.prop")

def create_advanced_flash_script():
    """إنشاء سكريبت فلاش متقدم"""
    advanced_script = """@echo off
echo ===== فلاش ROM معدل مع Magisk =====
echo تحذير: تأكد من فتح bootloader أولاً!
echo.

echo 1. فتح bootloader...
fastboot flashing unlock
fastboot flashing unlock_critical

echo.
echo 2. فلاش vbmeta معطل...
fastboot --disable-verity --disable-verification flash vbmeta vbmeta.img
fastboot --disable-verity --disable-verification flash vbmeta_system vbmeta_system.img
fastboot --disable-verity --disable-verification flash vbmeta_vendor vbmeta_vendor.img

echo.
echo 3. فلاش boot معدل...
if exist boot_patched.img (
    fastboot flash boot boot_patched.img
    echo تم فلاش boot مع Magisk
) else (
    fastboot flash boot boot.img
    echo تم فلاش boot عادي
)

echo.
echo 4. فلاش recovery...
fastboot flash recovery recovery.img

echo.
echo 5. فلاش super...
fastboot flash super super.img

echo.
echo 6. مسح البيانات...
fastboot -w

echo.
echo 7. إعادة التشغيل...
fastboot reboot

echo.
echo ===== تم الانتهاء! =====
echo الجهاز الآن بدون قيود ومع صلاحيات الروت
echo يمكنك تثبيت أي تطبيق خارجي
pause
"""
    
    with open('flash_rooted_rom.bat', 'w', encoding='utf-8') as f:
        f.write(advanced_script)
    
    print("تم إنشاء سكريبت الفلاش المتقدم: flash_rooted_rom.bat")

def main():
    """الدالة الرئيسية"""
    print("=== أداة Magisk لإزالة جميع القيود ===")
    print("هذه الأداة ستقوم بـ:")
    print("1. تحميل Magisk")
    print("2. تصحيح boot.img")
    print("3. تعطيل جميع ميزات الأمان")
    print("4. إنشاء ROM بدون قيود")
    print()
    
    # تنفيذ العمليات
    download_magisk()
    extract_magisk()
    patch_boot_image()
    create_rooted_system()
    disable_security_features()
    create_advanced_flash_script()
    
    print("\n=== تم الانتهاء من جميع العمليات ===")
    print("الملفات جاهزة:")
    print("- استخدم flash_rooted_rom.bat للفلاش الكامل")
    print("- boot_patched.img يحتوي على Magisk")
    print("- جميع ملفات vbmeta معطلة")
    print()
    print("تحذير: تأكد من فتح bootloader قبل الفلاش!")

if __name__ == "__main__":
    main()
